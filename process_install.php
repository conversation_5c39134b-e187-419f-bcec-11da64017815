<?php
// Silencia erros padrão
error_reporting(0);
ini_set('display_errors', 0);

// ---------- INÍCIO DO CÓDIGO ----------

$host     = isset($_POST['host'])     ? $_POST['host']     : null;
$user     = isset($_POST['user'])     ? $_POST['user']     : null;
$password = isset($_POST['password']) ? $_POST['password'] : null;
$dbname   = isset($_POST['dbname'])   ? $_POST['dbname']   : null;

$zipFile     = __DIR__ . '/site.zip';
$extractPath = __DIR__;
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Agendamento e Atendimento - Instalação</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #a5b4fc;
            --secondary: #64748b;
            --accent: #06b6d4;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --light: #f8fafc;
            --lighter: #ffffff;
            --dark: #1e293b;
            --darker: #0f172a;
            --border: #e2e8f0;
            --shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            color: var(--dark);
            line-height: 1.6;
            position: relative;
            overflow-x: hidden;
        }
        
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(120, 200, 255, 0.2) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundFloat 20s ease-in-out infinite;
        }
        
        @keyframes backgroundFloat {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            33% { transform: translate(30px, -30px) rotate(1deg); }
            66% { transform: translate(-20px, 20px) rotate(-1deg); }
        }
        
        .container {
            background: var(--gradient-card);
            padding: 50px;
            border-radius: 24px;
            box-shadow: var(--shadow);
            max-width: 700px;
            width: 100%;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            animation: slideInUp 0.8s ease-out;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), var(--accent), var(--success));
            border-radius: 24px 24px 0 0;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(60px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary), var(--accent));
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
            animation: logoFloat 3s ease-in-out infinite;
        }
        
        @keyframes logoFloat {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(5deg); }
        }
        
        .logo svg {
            width: 40px;
            height: 40px;
            stroke: white;
            stroke-width: 2.5;
        }
        
        .main-title {
            font-size: 32px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
            line-height: 1.2;
        }
        
        .subtitle {
            color: var(--secondary);
            font-size: 18px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .progress-section {
            display: none;
            margin: 30px 0;
        }
        
        .progress-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .progress-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--success), #059669);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .progress-icon svg {
            width: 24px;
            height: 24px;
            stroke: white;
            stroke-width: 2.5;
        }
        
        .progress-info h3 {
            font-size: 20px;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 5px;
        }
        
        .progress-info p {
            color: var(--secondary);
            font-size: 14px;
        }
        
        .progress-container {
            background: rgba(99, 102, 241, 0.1);
            border-radius: 25px;
            padding: 4px;
            margin-bottom: 20px;
            border: 1px solid rgba(99, 102, 241, 0.2);
        }
        
        .progress-bar {
            height: 20px;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            border-radius: 20px;
            width: 0%;
            transition: width 0.8s ease;
            position: relative;
            overflow: hidden;
        }
        
        .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 1.5s infinite;
        }
        
        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .progress-text {
            text-align: center;
            font-weight: 600;
            color: var(--primary);
            margin-top: 10px;
        }
        
        .step-list {
            list-style: none;
            padding: 0;
        }
        
        .step-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 12px;
            margin-bottom: 8px;
            border-radius: 12px;
            background: rgba(99, 102, 241, 0.05);
            border: 1px solid rgba(99, 102, 241, 0.1);
            transition: all 0.3s ease;
        }
        
        .step-item.active {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
            transform: translateX(5px);
        }
        
        .step-item.completed {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
        }
        
        .step-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--border);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .step-item.active .step-icon {
            background: var(--warning);
            animation: spin 2s linear infinite;
        }
        
        .step-item.completed .step-icon {
            background: var(--success);
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .step-icon svg {
            width: 16px;
            height: 16px;
            stroke: white;
            stroke-width: 2;
        }
        
        .step-text {
            font-weight: 500;
            color: var(--dark);
        }
        
        .message-container {
            margin: 30px 0;
        }
        
        .message {
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 15px;
            border: 1px solid;
            display: flex;
            align-items: center;
            gap: 15px;
            animation: messageSlide 0.5s ease-out;
        }
        
        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        .message.success {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
            color: #065f46;
        }
        
        .message.error {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.3);
            color: #991b1b;
        }
        
        .message.info {
            background: rgba(99, 102, 241, 0.1);
            border-color: rgba(99, 102, 241, 0.3);
            color: var(--primary-dark);
        }
        
        .message-icon {
            width: 24px;
            height: 24px;
            flex-shrink: 0;
        }
        
        .redirect-info {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(6, 182, 212, 0.1));
            padding: 25px;
            border-radius: 16px;
            text-align: center;
            margin-top: 30px;
            border: 2px solid rgba(16, 185, 129, 0.2);
        }
        
        .redirect-info h3 {
            color: var(--success);
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .redirect-info p {
            color: var(--secondary);
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .countdown {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: var(--success);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            animation: pulse 1s ease-in-out infinite;
        }
        
        .developer-footer {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: rgba(99, 102, 241, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }
        
        .developer-links {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .dev-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .website-link {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
        }
        
        .whatsapp-link {
            background: linear-gradient(135deg, #25d366, #128c7e);
            color: white;
        }
        
        .dev-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }
        
        @media (max-width: 640px) {
            .container {
                padding: 30px 25px;
                margin: 10px;
            }
            
            .main-title {
                font-size: 24px;
            }
            
            .developer-links {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
            </div>
            <h1 class="main-title">Sistema de Agendamento<br>e Atendimento</h1>
            <p class="subtitle">Processo de Instalação</p>
        </div>

        <?php
        /**
         * Extrai o site.zip
         */
        function extractZip($zipFile, $extractPath) {
            if (!file_exists($zipFile)) {
                echo '<div class="message error">';
                echo '<svg class="message-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                echo '<strong>ERRO:</strong> O arquivo `site.zip` não foi encontrado no mesmo diretório deste script.';
                echo '</div>';
                return false;
            }
            $zip = new ZipArchive;
            $res = $zip->open($zipFile);
            if ($res === TRUE) {
                if ($zip->extractTo($extractPath)) {
                    echo '<div class="message success">';
                    echo '<svg class="message-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>';
                    echo 'Arquivos extraídos com sucesso.';
                    echo '</div>';
                    echo '<script>updateProgress(25);</script>';
                    return true;
                } else {
                    echo '<div class="message error">';
                    echo '<svg class="message-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                    echo '<strong>ERRO:</strong> Falha ao extrair os arquivos. Verifique permissões.';
                    echo '</div>';
                    return false;
                }
                $zip->close();
            } else {
                echo '<div class="message error">';
                echo '<svg class="message-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                echo '<strong>ERRO:</strong> Falha ao abrir o arquivo `site.zip`.';
                echo '</div>';
                return false;
            }
        }

        /**
         * Cria conn.php
         */
        function createConnectionFile($host, $user, $password, $dbname) {
            $filePath = __DIR__ . '/login/painel/conn.php';
            $content  = "<?php\n";
            $content .= "/* Arquivo de conexão gerado automaticamente */\n\n";
            $content .= "\$servidor = '$host';\n";
            $content .= "\$usuario  = '$user';\n";
            $content .= "\$senha    = '$password';\n";
            $content .= "\$banco    = '$dbname';\n\n";
            $content .= "\$conn = mysqli_connect(\$servidor, \$usuario, \$senha, \$banco);\n";
            $content .= "if(!\$conn) { /* erro silencioso */ }\n";
            $content .= "else { mysqli_set_charset(\$conn, \"utf8mb4\"); }\n";
            $content .= "date_default_timezone_set('America/Sao_Paulo');\n";
            $content .= "?>";
            if (file_put_contents($filePath, $content) === false) {
                echo '<div class="message error">';
                echo '<svg class="message-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                echo '<strong>AVISO:</strong> Não foi possível criar `conn.php`.';
                echo '</div>';
                return false;
            } else {
                echo '<div class="message success">';
                echo '<svg class="message-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>';
                echo 'Arquivo de conexão `conn.php` criado com sucesso.';
                echo '</div>';
                echo '<script>updateProgress(50);</script>';
                return true;
            }
        }

        /**
         * Importa banco.sql usando conn.php já criado
         */
        function setupDatabase() {
            $sqlFile = __DIR__ . '/login/painel/banco.sql';
            if (!file_exists($sqlFile)) {
                echo '<div class="message error">';
                echo '<svg class="message-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                echo '<strong>ERRO:</strong> Arquivo `banco.sql` não encontrado.';
                echo '</div>';
                return false;
            }
            require __DIR__ . '/login/painel/conn.php';
            if (!$conn) {
                echo '<div class="message error">';
                echo '<svg class="message-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                echo '<strong>ERRO:</strong> Não foi possível conectar ao banco via conn.php.';
                echo '</div>';
                return false;
            }

            $sql = file_get_contents($sqlFile);

            // ====== LIMPEZA DO SQL ======
            $sql = str_replace(["DELIMITER $$", "DELIMITER ;"], "", $sql);
            $sql = str_replace("$$", ";", $sql);
            $sql = preg_replace('/DEFINER=`[^`]+`@`[^`]+`/i', '', $sql);

            // DROP PROCEDURE antes de cada CREATE
            $sql = preg_replace_callback(
                '/CREATE\s+PROCEDURE\s+`?([a-zA-Z0-9_]+)`?/i',
                function ($m) {
                    return "DROP PROCEDURE IF EXISTS `{$m[1]}`;\nCREATE PROCEDURE `{$m[1]}`";
                },
                $sql
            );
            // ====== FIM LIMPEZA ======

            if ($conn->multi_query($sql)) {
                do {
                    if ($result = $conn->store_result()) { $result->free(); }
                } while ($conn->more_results() && $conn->next_result());
                echo '<div class="message success">';
                echo '<svg class="message-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>';
                echo 'Banco de dados importado com sucesso.';
                echo '</div>';
                echo '<script>updateProgress(100);</script>';
                return true;
            } else {
                echo '<div class="message error">';
                echo '<svg class="message-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                echo '<strong>ERRO AO IMPORTAR O BANCO:</strong> ' . $conn->error;
                echo '</div>';
                return false;
            }

            $conn->close();
        }

        // --- LÓGICA PRINCIPAL ---
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            if ($host && $user && $dbname) {
                echo '
                <div class="progress-section" id="progressSection">
                    <div class="progress-header">
                        <div class="progress-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                            </svg>
                        </div>
                        <div class="progress-info">
                            <h3>Instalando Sistema</h3>
                            <p>Aguarde enquanto configuramos tudo para você...</p>
                        </div>
                    </div>
                    
                    <div class="progress-container">
                        <div class="progress-bar" id="progressBar"></div>
                    </div>
                    <div class="progress-text" id="progressText">Iniciando instalação...</div>
                    
                    <ul class="step-list">
                        <li class="step-item" id="step1">
                            <div class="step-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                                </svg>
                            </div>
                            <span class="step-text">Extraindo arquivos do sistema</span>
                        </li>
                        <li class="step-item" id="step2">
                            <div class="step-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            </div>
                            <span class="step-text">Configurando conexão com banco</span>
                        </li>
                        <li class="step-item" id="step3">
                            <div class="step-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                                </svg>
                            </div>
                            <span class="step-text">Importando estrutura do banco</span>
                        </li>
                        <li class="step-item" id="step4">
                            <div class="step-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <span class="step-text">Finalizando instalação</span>
                        </li>
                    </ul>
                </div>
                
                <div class="message-container" id="messageContainer">
                ';
                
                echo '<script>
                    document.getElementById("progressSection").style.display = "block";
                    document.getElementById("step1").classList.add("active");
                    
                    function updateProgress(percent) {
                        const bar = document.getElementById("progressBar");
                        const text = document.getElementById("progressText");
                        bar.style.width = percent + "%";
                        
                        if(percent === 25) {
                            text.textContent = "Arquivos extraídos (25%)";
                            document.getElementById("step1").classList.remove("active");
                            document.getElementById("step1").classList.add("completed");
                            document.getElementById("step2").classList.add("active");
                        } else if(percent === 50) {
                            text.textContent = "Conexão configurada (50%)";
                            document.getElementById("step2").classList.remove("active");
                            document.getElementById("step2").classList.add("completed");
                            document.getElementById("step3").classList.add("active");
                        } else if(percent === 100) {
                            text.textContent = "Instalação concluída (100%)";
                            document.getElementById("step3").classList.remove("active");
                            document.getElementById("step3").classList.add("completed");
                            document.getElementById("step4").classList.add("active");
                            setTimeout(() => {
                                document.getElementById("step4").classList.remove("active");
                                document.getElementById("step4").classList.add("completed");
                            }, 500);
                        }
                    }
                </script>';
                
                $success = true;
                
                if (!extractZip($zipFile, $extractPath)) $success = false;
                if ($success && !createConnectionFile($host, $user, $password, $dbname)) $success = false;
                if ($success && !setupDatabase()) $success = false;
                
                echo '</div>';
                
                if ($success) {
                    echo '
                    <div class="redirect-info">
                        <h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Instalação Concluída com Sucesso!
                        </h3>
                        <p>Seu sistema está pronto para uso. Você será redirecionado para a página inicial em:</p>
                        <div class="countdown">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span id="countdown">3</span> segundos
                        </div>
                    </div>
                    
                    <script>
                        let timeLeft = 3;
                        const countdownElement = document.getElementById("countdown");
                        
                        const timer = setInterval(() => {
                            timeLeft--;
                            countdownElement.textContent = timeLeft;
                            
                            if (timeLeft <= 0) {
                                clearInterval(timer);
                                window.location.href = "index.php";
                            }
                        }, 1000);
                    </script>
                    <meta http-equiv="refresh" content="3;url=index.php">
                    ';
                }
            } else {
                echo '<div class="message error">';
                echo '<svg class="message-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                echo '<strong>ERRO:</strong> Todos os campos (exceto senha) são obrigatórios.';
                echo '</div>';
            }
        } else {
            echo '<div class="message info">';
            echo '<svg class="message-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>';
            echo 'Por favor, preencha o formulário de instalação para iniciar o processo.';
            echo '</div>';
        }
        ?>

        <div class="developer-footer">
            <div class="developer-links">
                <a href="https://www.editacodigo.com.br/" target="_blank" class="dev-link website-link">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                    </svg>
                    Edita Código
                </a>
                <a href="https://wa.me/5531984767330" target="_blank" class="dev-link whatsapp-link">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                    </svg>
                    Suporte WhatsApp
                </a>
            </div>
        </div>
    </div>
</body>
</html>